/**
 * table-handlers.js
 * Gestión de tablas y navegación por pestañas para el módulo de logística
 * 
 * Contiene funciones para navegación entre pestañas, gestión de eventos
 * de tabla, búsqueda y filtrado.
 */

// Función para mostrar/ocultar pestañas
function mostrarPestana(pestana) {
    ModLogistica.debugLog('Mostrando pestaña:', pestana);

    // Ocultar todas las pestañas
    const pestanas = ['directa', 'faltante', 'recepcion', 'reversa'];
    pestanas.forEach(function(p) {
        const elemento = document.getElementById(p);
        if (elemento) {
            elemento.style.display = 'none';
        }
    });

    // Mostrar la pestaña seleccionada
    const pestanaSeleccionada = document.getElementById(pestana);
    if (pestanaSeleccionada) {
        pestanaSeleccionada.style.display = 'block';
    }

    // Actualizar estado de los botones de navegación
    actualizarEstadoBotones(pestana);
}

// Función para actualizar el estado visual de los botones de navegación
function actualizarEstadoBotones(pestanaActiva) {
    const botones = document.querySelectorAll('.nav-link');
    botones.forEach(function(boton) {
        boton.classList.remove('active');
    });

    // Marcar el botón activo
    const botonActivo = document.querySelector(`[onclick="mostrarPestana('${pestanaActiva}')"]`);
    if (botonActivo) {
        botonActivo.classList.add('active');
    }
}

// Función para cargar historial (implementación completa)
function cargarHistorial(serie) {
    ModLogistica.debugLog('Cargando historial para serie:', serie);

    // Limpiar el contenido del elemento web antes de enviar la solicitud
    document.getElementById('webHistorial').innerHTML = '<div class="loading-message">Cargando historial...</div>';

    // Crear nueva instancia de XMLHttpRequest
    const xhr = new XMLHttpRequest();
    xhr.open('POST', `${window.ModLogisticaConfig.endpoints.historial}?proceso=historial2&serie=${encodeURIComponent(serie)}`, true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                // Intentar parsear como JSON primero
                const response = JSON.parse(xhr.responseText);

                // Verificar si es un array directo (formato actual del endpoint)
                if (Array.isArray(response)) {
                    mostrarHistorialFormateado(response, serie);
                }
                // Verificar si es un objeto con success y data (formato alternativo)
                else if (response.success && response.data) {
                    mostrarHistorialFormateado(response.data, serie);
                }
                // Si es otro tipo de JSON, mostrar como HTML directo
                else {
                    document.getElementById('webHistorial').innerHTML = xhr.responseText;
                }
            } catch (e) {
                // Si falla el parsing JSON, mostrar como HTML directo
                document.getElementById('webHistorial').innerHTML = xhr.responseText;
            }
        } else {
            ModLogistica.errorLog("Error al recibir la respuesta: ", xhr.statusText);
            document.getElementById('webHistorial').innerHTML =
                '<div class="alert alert-danger">Error al cargar el historial. Por favor intente nuevamente.</div>';
        }
    };

    xhr.onerror = function() {
        ModLogistica.errorLog("Error de conexión al cargar historial");
        document.getElementById('webHistorial').innerHTML = 
            '<div class="alert alert-danger">Error de conexión. Por favor intente nuevamente.</div>';
    };

    xhr.send(); // Enviar la solicitud
}

// Función para mostrar historial formateado
function mostrarHistorialFormateado(data, serie) {
    ModLogistica.debugLog('Mostrando historial formateado para serie:', serie);
    ModLogistica.debugLog('Datos recibidos:', data);

    let html = `<div class="historial-container">
        <h6 class="historial-title">Historial de movimientos - Serie: ${serie}</h6>`;

    if (data && data.length > 0) {
        data.forEach(function(item, index) {
            ModLogistica.debugLog(`Procesando item ${index + 1}:`, item);

            html += `<div class="timeline-item">
                <div class="timeline-marker">${index + 1}</div>
                <div class="timeline-content">
                    <div class="card-header">
                        <h6 class="card-title">${item.Semantica || item.tipo_movimiento || 'Movimiento'}</h6>
                        <span class="card-date">${item.fecha_hora || 'Sin fecha'}</span>
                    </div>`;

            // Mostrar técnico origen
            if (item.Nombre_origen) {
                html += `<p class="card-info"><strong>Técnico Origen:</strong> ${item.Nombre_origen}</p>`;
            }

            // Mostrar técnico destino
            if (item.Nombre_destino) {
                html += `<p class="card-info"><strong>Técnico Destino:</strong> ${item.Nombre_destino}</p>`;
            }

            // Mostrar observación
            if (item.observacion) {
                const textoCorto = item.observacion.substring(0, 30);
                const textoMostrar = (item.observacion.length > 30) ? textoCorto + '...' : textoCorto;
                html += `<p class="card-info" title="${item.observacion}"><strong>Observación:</strong> ${textoMostrar}</p>`;
            }

            // Mostrar archivo adjunto - MEJORADO
            if (item.archivo_adj) {
                ModLogistica.debugLog('Archivo adjunto encontrado:', item.archivo_adj);
                html += `<p class="card-info archivo-info">
                    <strong>📎 Archivo:</strong>
                    <a href="${item.archivo_adj}" download class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-download"></i> Descargar respaldo
                    </a>
                </p>`;
            } else {
                ModLogistica.debugLog('No se encontró archivo adjunto para este item');
            }

            html += `</div></div>`;
        });
    } else {
        html += '<div class="alert alert-info">No hay historial disponible para esta serie.</div>';
    }

    html += '</div>';
    document.getElementById('webHistorial').innerHTML = html;
    ModLogistica.debugLog('Historial renderizado correctamente');
}

// Función para reinicializar event listeners después de actualizar tablas
function inicializarEventListenersTabla() {
    ModLogistica.debugLog('Reinicializando event listeners de tabla');

    // Eventos para botones de historial
    document.querySelectorAll('.historial-btn').forEach(function(button) {
        button.removeEventListener('click', handleHistorialClick); // Remover listener anterior
        button.addEventListener('click', handleHistorialClick);
    });

    // Eventos para botones de declarar
    document.querySelectorAll('.declarar-btn').forEach(function(button) {
        button.removeEventListener('click', handleDeclararClick);
        button.addEventListener('click', handleDeclararClick);
    });

    // Eventos para botones de justificar
    document.querySelectorAll('.justificar-btn').forEach(function(button) {
        button.removeEventListener('click', handleJustificarClick);
        button.addEventListener('click', handleJustificarClick);
    });
}

// Handlers separados para evitar duplicación de listeners
function handleHistorialClick() {
    const row = this.closest('tr');
    const serial = row.cells[0].textContent;
    const tipo = row.getAttribute('data-type');
    redirigirEnTransferencia(serial, tipo, 0, 'historial');
}

function handleDeclararClick() {
    const row = this.closest('tr');
    const serial = row.cells[0].textContent;
    const tipo = row.getAttribute('data-type');
    redirigirEnTransferencia(serial, tipo, 0, 'declarar');
}

function handleJustificarClick() {
    const row = this.closest('tr');
    const serial = row.cells[0].textContent;
    const tipo = row.getAttribute('data-type');
    redirigirEnTransferencia(serial, tipo, 0, 'justificar');
}

// Función para búsqueda en tablas
function buscarEnTabla(inputId, tablaId) {
    const input = document.getElementById(inputId);
    const tabla = document.getElementById(tablaId);
    
    if (!input || !tabla) return;

    const filtro = input.value.toUpperCase();
    const filas = tabla.getElementsByTagName('tr');

    for (let i = 1; i < filas.length; i++) { // Empezar desde 1 para saltar el header
        const fila = filas[i];
        const celdas = fila.getElementsByTagName('td');
        let mostrarFila = false;

        for (let j = 0; j < celdas.length; j++) {
            const celda = celdas[j];
            if (celda) {
                const textoCelda = celda.textContent || celda.innerText;
                if (textoCelda.toUpperCase().indexOf(filtro) > -1) {
                    mostrarFila = true;
                    break;
                }
            }
        }

        fila.style.display = mostrarFila ? '' : 'none';
    }

    ModLogistica.debugLog('Búsqueda realizada en tabla:', tablaId, 'con filtro:', filtro);
}

// Función para exportar tabla a Excel
function exportarTablaExcel(tablaId, nombreArchivo) {
    const tabla = document.getElementById(tablaId);
    if (!tabla) {
        ModLogistica.errorLog('Tabla no encontrada:', tablaId);
        return;
    }

    // Crear un libro de trabajo
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.table_to_sheet(tabla);

    // Agregar la hoja al libro
    XLSX.utils.book_append_sheet(wb, ws, 'Datos');

    // Descargar el archivo
    XLSX.writeFile(wb, `${nombreArchivo}.xlsx`);

    ModLogistica.debugLog('Tabla exportada:', nombreArchivo);
}

// Función para filtrar tabla por columna
function filtrarTablaPorColumna(tablaId, columnaIndex, valor) {
    const tabla = document.getElementById(tablaId);
    if (!tabla) return;

    const filas = tabla.getElementsByTagName('tr');

    for (let i = 1; i < filas.length; i++) { // Empezar desde 1 para saltar el header
        const fila = filas[i];
        const celda = fila.getElementsByTagName('td')[columnaIndex];
        
        if (celda) {
            const textoCelda = celda.textContent || celda.innerText;
            const mostrarFila = valor === '' || textoCelda.toLowerCase().includes(valor.toLowerCase());
            fila.style.display = mostrarFila ? '' : 'none';
        }
    }

    ModLogistica.debugLog('Filtro aplicado en columna:', columnaIndex, 'con valor:', valor);
}

// Función para ordenar tabla por columna
function ordenarTabla(tablaId, columnaIndex, ascendente = true) {
    const tabla = document.getElementById(tablaId);
    if (!tabla) return;

    const tbody = tabla.getElementsByTagName('tbody')[0];
    const filas = Array.from(tbody.getElementsByTagName('tr'));

    filas.sort(function(a, b) {
        const celdaA = a.getElementsByTagName('td')[columnaIndex];
        const celdaB = b.getElementsByTagName('td')[columnaIndex];

        if (!celdaA || !celdaB) return 0;

        const textoA = celdaA.textContent || celdaA.innerText;
        const textoB = celdaB.textContent || celdaB.innerText;

        // Intentar comparar como números si es posible
        const numeroA = parseFloat(textoA);
        const numeroB = parseFloat(textoB);

        if (!isNaN(numeroA) && !isNaN(numeroB)) {
            return ascendente ? numeroA - numeroB : numeroB - numeroA;
        }

        // Comparar como texto
        return ascendente ? 
            textoA.localeCompare(textoB) : 
            textoB.localeCompare(textoA);
    });

    // Reordenar las filas en el DOM
    filas.forEach(function(fila) {
        tbody.appendChild(fila);
    });

    ModLogistica.debugLog('Tabla ordenada por columna:', columnaIndex, 'ascendente:', ascendente);
}

// Función para inicializar funcionalidades de tabla
function inicializarFuncionalidadesTabla() {
    ModLogistica.debugLog('Inicializando funcionalidades de tabla');

    // Agregar funcionalidad de búsqueda si existen campos de búsqueda
    const camposBusqueda = document.querySelectorAll('[data-search-table]');
    camposBusqueda.forEach(function(campo) {
        const tablaId = campo.getAttribute('data-search-table');
        campo.addEventListener('input', function() {
            buscarEnTabla(campo.id, tablaId);
        });
    });

    // Agregar funcionalidad de ordenamiento a headers clickeables
    const headersOrdenables = document.querySelectorAll('[data-sortable]');
    headersOrdenables.forEach(function(header, index) {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const tablaId = this.closest('table').id;
            const ascendente = !this.classList.contains('sorted-desc');
            
            // Remover clases de ordenamiento de otros headers
            headersOrdenables.forEach(function(h) {
                h.classList.remove('sorted-asc', 'sorted-desc');
            });

            // Agregar clase de ordenamiento al header actual
            this.classList.add(ascendente ? 'sorted-asc' : 'sorted-desc');

            ordenarTabla(tablaId, index, ascendente);
        });
    });
}
